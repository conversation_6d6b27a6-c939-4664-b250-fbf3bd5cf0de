import React from "react";
import { getDisplayNameForModel } from "@/lib/hooks";
import {
  checkLLMSupportsImageInput,
  destructureValue,
  structureValue,
} from "@/lib/llm/utils";
import {
  getProviderIcon,
  LLMProviderDescriptor,
} from "@/app/admin/configuration/llm/interfaces";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LLMSelectorProps {
  userSettings?: boolean;
  llmProviders: LLMProviderDescriptor[];
  currentLlm: string | null;
  onSelect: (value: string | null) => void;
  requiresImageGeneration?: boolean;
  availableModels?: string[];
}

export const LLMSelector: React.FC<LLMSelectorProps> = ({
  userSettings,
  llmProviders,
  currentLlm,
  onSelect,
  requiresImageGeneration,
  availableModels,
}) => {
  let llmOptions = llmProviders.flatMap((provider) => {
    return (provider.display_model_names || provider.model_names)
      .map((modelName) => ({
        name: getDisplayNameForModel(modelName),
        value: structureValue(provider.name, provider.provider, modelName),
        icon: getProviderIcon(provider.provider, modelName),
        displayName: provider.name,
      }));
  });


  const defaultProvider = llmProviders.find(
    (llmProvider) => llmProvider.is_default_provider
  );

  const defaultModelName = defaultProvider?.default_model_name;
  const defaultModelDisplayName = defaultModelName
    ? getDisplayNameForModel(defaultModelName)
    : null;

  const destructuredCurrentValue = currentLlm
    ? destructureValue(currentLlm)
    : null;

  const currentLlmName = destructuredCurrentValue?.modelName;
  const currentProvider = destructuredCurrentValue?.name
    ? llmProviders.find(provider => provider.name === destructuredCurrentValue.name)
    : null;

  return (
    <Select
      value={currentLlm ? currentLlm : "default"}
      onValueChange={(value) => onSelect(value === "default" ? null : value)}
    >
      <SelectTrigger className="min-w-40">
        <SelectValue>
          {currentLlmName
            ? `${getDisplayNameForModel(currentLlmName) || ''} (${currentProvider?.name})`
            : userSettings
              ? "System Default"
              : "User Default"}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="z-[99999]">
        <SelectItem className="flex" hideCheck value="default">
          <span>{userSettings ? "System Default" : "User Default"}</span>
          {userSettings && (
            <span className="my-auto font-normal ml-1">
              ({defaultModelDisplayName})
            </span>
          )}
        </SelectItem>
        {llmOptions.map((option) => {
          if (
            !requiresImageGeneration ||
            checkLLMSupportsImageInput(option.name)
          ) {
            return (
              <SelectItem key={option.value} value={option.value}>
                <div className="my-1 flex items-center">
                  {option.icon && option.icon({ size: 16 })}
                  <div className="ml-2 flex flex-col">
                    <span className="text-sm font-medium">{option.name}</span>
                    <span className="text-xs text-muted-foreground">({option.displayName})</span>
                  </div>
                </div>
              </SelectItem>
            );
          }
          return null;
        })}
      </SelectContent>
    </Select>
  );
};
